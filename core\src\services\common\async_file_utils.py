import asyncio
import aiofiles
import aiofiles.os
import hashlib
import shutil
import os
import time
from pathlib import Path
from typing import Optional, List, Dict, Any, Callable
from concurrent.futures import ThreadPoolExecutor
import logging
from .resource_limiter import get_resource_limiter, FileOperationContext

logger = logging.getLogger(__name__)

class AsyncFileUtils:
    """异步文件操作工具类"""
    
    def __init__(self, max_workers: int = 10):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.semaphore = asyncio.Semaphore(max_workers)
        self.resource_limiter = get_resource_limiter()
    
    async def copy_file_async(self, source: str, target: str,
                             progress_callback: Optional[Callable] = None) -> bool:
        """异步复制文件"""
        async with FileOperationContext(self.resource_limiter, "copy_file"):
            try:
                async with self.semaphore:
                    loop = asyncio.get_event_loop()
                    await loop.run_in_executor(
                        self.executor,
                        self._copy_file_sync,
                        source, target, progress_callback
                    )
                    return True
            except Exception as e:
                logger.error(f"异步复制文件失败 {source} -> {target}: {str(e)}")
                return False
    
    def _copy_file_sync(self, source: str, target: str, 
                       progress_callback: Optional[Callable] = None):
        """同步复制文件（在线程池中执行）"""
        # 确保目标目录存在
        os.makedirs(os.path.dirname(target), exist_ok=True)
        
        # 获取文件大小用于进度计算
        file_size = os.path.getsize(source)
        copied_size = 0
        
        with open(source, 'rb') as src, open(target, 'wb') as dst:
            while True:
                chunk = src.read(64 * 1024)  # 64KB chunks
                if not chunk:
                    break
                dst.write(chunk)
                copied_size += len(chunk)
                
                # 调用进度回调
                if progress_callback:
                    progress = copied_size / file_size
                    progress_callback(progress)
        
        # 复制文件元数据
        shutil.copystat(source, target)
    
    async def move_file_async(self, source: str, target: str) -> bool:
        """异步移动文件"""
        async with FileOperationContext(self.resource_limiter, "move_file"):
            try:
                async with self.semaphore:
                    loop = asyncio.get_event_loop()
                    await loop.run_in_executor(
                        self.executor,
                        shutil.move,
                        source, target
                    )
                    return True
            except Exception as e:
                logger.error(f"异步移动文件失败 {source} -> {target}: {str(e)}")
                return False
    
    async def delete_file_async(self, file_path: str) -> bool:
        """异步删除文件"""
        try:
            async with self.semaphore:
                if os.path.isfile(file_path):
                    await aiofiles.os.remove(file_path)
                elif os.path.isdir(file_path):
                    loop = asyncio.get_event_loop()
                    await loop.run_in_executor(
                        self.executor, 
                        shutil.rmtree, 
                        file_path
                    )
                return True
        except Exception as e:
            logger.error(f"异步删除文件失败 {file_path}: {str(e)}")
            return False
    
    async def calculate_md5_async(self, file_path: str) -> Optional[str]:
        """异步计算文件MD5"""
        try:
            async with self.semaphore:
                loop = asyncio.get_event_loop()
                return await loop.run_in_executor(
                    self.executor, 
                    self._calculate_md5_sync, 
                    file_path
                )
        except Exception as e:
            logger.error(f"异步计算MD5失败 {file_path}: {str(e)}")
            return None
    
    def _calculate_md5_sync(self, file_path: str) -> str:
        """同步计算MD5（在线程池中执行）"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    async def read_file_async(self, file_path: str) -> Optional[bytes]:
        """异步读取文件"""
        try:
            async with aiofiles.open(file_path, 'rb') as f:
                return await f.read()
        except Exception as e:
            logger.error(f"异步读取文件失败 {file_path}: {str(e)}")
            return None
    
    async def write_file_async(self, file_path: str, content: bytes) -> bool:
        """异步写入文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(content)
            return True
        except Exception as e:
            logger.error(f"异步写入文件失败 {file_path}: {str(e)}")
            return False
    
    async def batch_copy_files(self, operations: List[Dict[str, str]], 
                              max_concurrent: int = 5,
                              progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """批量异步复制文件"""
        semaphore = asyncio.Semaphore(max_concurrent)
        results = {"success": 0, "failed": 0, "errors": []}
        
        async def copy_single(operation):
            async with semaphore:
                source = operation["source"]
                target = operation["target"]
                success = await self.copy_file_async(source, target)
                
                if success:
                    results["success"] += 1
                else:
                    results["failed"] += 1
                    results["errors"].append(f"复制失败: {source} -> {target}")
                
                if progress_callback:
                    total = results["success"] + results["failed"]
                    progress_callback(total, len(operations))
        
        # 并发执行所有复制操作
        tasks = [copy_single(op) for op in operations]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        return results
    
    async def batch_move_files(self, operations: List[Dict[str, str]], 
                              max_concurrent: int = 5,
                              progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """批量异步移动文件"""
        semaphore = asyncio.Semaphore(max_concurrent)
        results = {"success": 0, "failed": 0, "errors": []}
        
        async def move_single(operation):
            async with semaphore:
                source = operation["source"]
                target = operation["target"]
                success = await self.move_file_async(source, target)
                
                if success:
                    results["success"] += 1
                else:
                    results["failed"] += 1
                    results["errors"].append(f"移动失败: {source} -> {target}")
                
                if progress_callback:
                    total = results["success"] + results["failed"]
                    progress_callback(total, len(operations))
        
        # 并发执行所有移动操作
        tasks = [move_single(op) for op in operations]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        return results
    
    async def batch_delete_files(self, file_paths: List[str], 
                                max_concurrent: int = 5,
                                progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """批量异步删除文件"""
        semaphore = asyncio.Semaphore(max_concurrent)
        results = {"success": 0, "failed": 0, "errors": []}
        
        async def delete_single(file_path):
            async with semaphore:
                success = await self.delete_file_async(file_path)
                
                if success:
                    results["success"] += 1
                else:
                    results["failed"] += 1
                    results["errors"].append(f"删除失败: {file_path}")
                
                if progress_callback:
                    total = results["success"] + results["failed"]
                    progress_callback(total, len(file_paths))
        
        # 并发执行所有删除操作
        tasks = [delete_single(path) for path in file_paths]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        return results
    
    def __del__(self):
        """清理资源"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)

# 全局实例
_async_file_utils = None

def get_async_file_utils() -> AsyncFileUtils:
    """获取异步文件工具实例"""
    global _async_file_utils
    if _async_file_utils is None:
        _async_file_utils = AsyncFileUtils()
    return _async_file_utils
